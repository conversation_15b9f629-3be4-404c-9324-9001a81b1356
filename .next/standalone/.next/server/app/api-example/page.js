(()=>{var e={};e.id=230,e.ids=[230],e.modules={440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var n=t(1658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},485:()=>{},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},2667:e=>{e.exports={page:"page_page__ZU32B",main:"page_main__GlU4n",ctas:"page_ctas__g5wGe",primary:"page_primary__V8M9Y",secondary:"page_secondary__lm_PT",footer:"page_footer__sHKi3",logo:"page_logo__7fc9l"}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3984:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},4256:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d,metadata:()=>l});var n=t(7413),s=t(2202),o=t.n(s),a=t(4988),i=t.n(a);t(1135);let l={title:"Create Next App",description:"Generated by create next app"};function d({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${o().variable} ${i().variable}`,children:e})})}},6384:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});let n=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/it-sentinel-frontend/src/app/api-example/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/it-sentinel-frontend/src/app/api-example/page.tsx","default")},6860:(e,r,t)=>{Promise.resolve().then(t.bind(t,9518))},6933:()=>{},8716:(e,r,t)=>{Promise.resolve().then(t.bind(t,6384))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9414:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>m,tree:()=>d});var n=t(5239),s=t(8088),o=t(8170),a=t.n(o),i=t(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["api-example",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6384)),"/home/<USER>/it-sentinel-frontend/src/app/api-example/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/home/<USER>/it-sentinel-frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["/home/<USER>/it-sentinel-frontend/src/app/api-example/page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/api-example/page",pathname:"/api-example",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9518:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var n=t(687),s=t(3210),o=t(2667),a=t.n(o);function i(){let[e,r]=(0,s.useState)(null),[t,o]=(0,s.useState)(!1),[i,l]=(0,s.useState)(null),d=async()=>{o(!0),l(null);try{await new Promise(e=>setTimeout(e,1e3)),r({id:1,title:"Ejemplo de API",message:"Conexi\xf3n con backend exitosa (simulada)"})}catch(e){l(e instanceof Error?e.message:"Error al obtener datos")}finally{o(!1)}};return(0,n.jsx)("div",{className:a().page,children:(0,n.jsxs)("main",{className:a().main,children:[(0,n.jsx)("h1",{children:"Ejemplo de Uso de API"}),(0,n.jsxs)("p",{children:["URL de la API: ","http://localhost/api"]}),(0,n.jsx)("div",{style:{margin:"20px 0"},children:(0,n.jsx)("button",{onClick:d,style:{padding:"10px 20px",backgroundColor:"#0070f3",color:"white",border:"none",borderRadius:"5px",cursor:"pointer"},disabled:t,children:t?"Cargando...":"Obtener Datos"})}),i&&(0,n.jsxs)("div",{style:{color:"red",margin:"10px 0"},children:["Error: ",i]}),e&&(0,n.jsxs)("div",{style:{border:"1px solid #ddd",borderRadius:"5px",padding:"20px",marginTop:"20px",backgroundColor:"#f9f9f9"},children:[(0,n.jsx)("h2",{children:e.title}),(0,n.jsx)("p",{children:e.message}),(0,n.jsx)("pre",{children:JSON.stringify(e,null,2)})]}),(0,n.jsx)("h2",{children:"C\xf3mo usar la API"}),(0,n.jsx)("div",{style:{backgroundColor:"#f1f1f1",padding:"20px",borderRadius:"5px",maxWidth:"600px",margin:"20px 0",overflow:"auto"},children:(0,n.jsx)("pre",{style:{margin:0},children:`// Ejemplos de uso:

// GET request
const getData = async () => {
  try {
    const result = await api.get('/endpoint');
    console.log(result);
  } catch (error) {
    console.error(error);
  }
};

// POST request
const createData = async () => {
  try {
    const data = { name: 'Ejemplo', value: 123 };
    const result = await api.post('/endpoint', data);
    console.log(result);
  } catch (error) {
    console.error(error);
  }
};
`})}),(0,n.jsx)("div",{style:{marginTop:"20px"},children:(0,n.jsx)("a",{href:"/",style:{color:"#0070f3",textDecoration:"none"},children:"← Volver a la p\xe1gina principal"})})]})})}},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[447,169,658],()=>t(9414));module.exports=n})();